---
export interface Props {
  title?: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  lang?: string;
  canonicalURL?: string;
  type?: 'website' | 'article';
  noindex?: boolean;
}

const {
  title,
  description,
  keywords,
  ogImage,
  lang = 'vi',
  canonicalURL,
  type = 'website',
  noindex = false
} = Astro.props;

import LucideIcon from '../components/ui/icons/LucideIcon.astro';
import HeaderWrapper from '../components/layout/headers/HeaderWrapper.astro';
import SEO from '../components/seo/SEO.astro';
---

<!DOCTYPE html>
<html lang={lang}>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <!-- Dark mode script - thêm trước khi trang tải để tránh nhấp nháy -->
  <script is:inline>
    // Lấy theme từ localStorage hoặc preferences
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const isDark = savedTheme === 'dark' || (!savedTheme && prefersDark);
    
    // Áp dụng class dark nếu cần
    if (isDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
    
    // Log để debug
    console.log('🌙 Initial dark mode setup:', { savedTheme, prefersDark, isDark });
  </script>
  
  <SEO 
    title={title}
    description={description}
    keywords={keywords}
    image={ogImage}
    url={canonicalURL}
    type={type}
    locale={lang === 'vi' ? 'vi_VN' : lang === 'en' ? 'en_US' : 'es_ES'}
    noindex={noindex}
  />

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet" />
  
  <!-- Preload critical resources -->
  <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin />
  
  <!-- Critical CSS inline -->
  <style>
    /* Critical above-the-fold styles */
    body { 
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
      display: flex;
      flex-direction: column;
      min-height: 100vh;
      margin: 0;
    }
    
    main {
      flex: 1;
    }
    
    /* Header and footer transition */
    header, footer {
      transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    }
    
    .hero-section { min-height: 100vh; }
    
    /* Loading spinner */
    .loading-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid currentColor;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    
    /* Fullscreen test mode - hide header and footer */
    body.fullscreen-test header,
    body.fullscreen-test footer {
      opacity: 0 !important;
      visibility: hidden !important;
      height: 0 !important;
      overflow: hidden !important;
      pointer-events: none !important;
      position: absolute !important;
      z-index: -1 !important;
    }
    
    /* Target the specific header with its exact class */
    body.fullscreen-test header.fixed,
    body.fullscreen-test header[class*="fixed"],
    body.fullscreen-test header[class*="top-0"],
    body.fullscreen-test header[class*="z-50"] {
      display: none !important;
      opacity: 0 !important;
      visibility: hidden !important;
      transform: translateY(-100%) !important;
    }
    
    body.fullscreen-test main {
      padding: 0;
      margin: 0;
    }
    
    body.fullscreen-test {
      background-color: #f9fafb;
    }
    
    /* Dark mode styles */
    .dark body.fullscreen-test {
      background-color: #111827;
    }

    /* Loại bỏ tất cả hiệu ứng focus và border trên toàn hệ thống */
    input,
    select,
    textarea,
    button,
    [role="combobox"],
    [role="listbox"],
    [role="textbox"],
    [role="button"],
    .focus-visible {
      transition: none !important;
      border-color: inherit !important;
    }

    input:focus, 
    select:focus, 
    textarea:focus, 
    button:focus,
    [role="combobox"]:focus,
    [role="listbox"]:focus,
    [role="textbox"]:focus,
    [role="button"]:focus,
    .focus-visible:focus,
    input:hover,
    select:hover,
    textarea:hover,
    button:hover,
    [role="combobox"]:hover,
    [role="listbox"]:hover,
    [role="textbox"]:hover,
    [role="button"]:hover,
    .focus-visible:hover,
    input:active,
    select:active,
    textarea:active,
    button:active,
    [role="combobox"]:active,
    [role="listbox"]:active,
    [role="textbox"]:active,
    [role="button"]:active,
    .focus-visible:active {
      outline: none !important;
      box-shadow: none !important;
      border-color: inherit !important;
      -webkit-box-shadow: none !important;
      -moz-box-shadow: none !important;
      appearance: none !important;
      -webkit-appearance: none !important;
      -moz-appearance: none !important;
      transition: none !important;
    }

    /* Đảm bảo không có hiệu ứng nhấp nháy - NHƯNG GIỮ LẠI cho input fields */
    *:not(input):not(textarea):not(select):not([data-popup]):not([data-popup] *) {
      transition: none !important;
      animation-duration: 0s !important;
      animation-delay: 0s !important;
    }

    /* Vô hiệu hóa transition cho dark mode - NHƯNG GIỮ LẠI cho input fields */
    html:not(input):not(textarea):not(select),
    html.dark:not(input):not(textarea):not(select),
    html.dark *:not(input):not(textarea):not(select):not([data-popup]):not([data-popup] *),
    *[class*="dark:"]:not(input):not(textarea):not(select) {
      transition: none !important;
    }

    /* 🚀 Siêu tối ưu popup animations - Hardware accelerated */
    [data-popup],
    .framer-motion-popup,
    [class*="motion-"] {
      will-change: transform, opacity;
      transform: translateZ(0);
      backface-visibility: hidden;
      perspective: 1000px;
    }

    /* Tối ưu cho Framer Motion */
    [data-framer-motion] {
      transform: translateZ(0);
    }

    /* 🎯 Cho phép input fields có transition cho dark mode */
    input, textarea, select {
      transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease !important;
    }

    /* Đảm bảo input fields trong popup cũng có transition */
    [data-popup] input,
    [data-popup] textarea,
    [data-popup] select {
      transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease !important;
    }

    /* 🎯 Đảm bảo input fields có transition mượt mà cho dark mode */
    input, textarea, select {
      transition: background-color 0.15s ease, color 0.15s ease, border-color 0.15s ease !important;
    }

    /* 🎯 Custom input classes cho popup forms */
    .popup-input {
      background-color: #ffffff !important;
      color: #111827 !important;
      border-color: #e5e7eb !important;
      transition: background-color 0.15s ease, color 0.15s ease, border-color 0.15s ease !important;
    }

    .dark .popup-input {
      background-color: #1f2937 !important;
      color: #ffffff !important;
      border-color: #4b5563 !important;
    }
  </style>
</head>

<body class="bg-gray-50 dark:bg-gray-900 font-inter antialiased">
  <!-- Skip to main content for accessibility -->
  <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50">
    Skip to main content
  </a>

  <!-- Header -->
  <HeaderWrapper />

  <!-- Main Content -->
  <main id="main-content" role="main" class="flex-1">
    <slot />
  </main>

  <!-- Footer -->
  <footer class="relative bg-gradient-to-br from-slate-900 via-gray-900 to-slate-800 text-white overflow-hidden mt-auto">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, white 1px, transparent 0); background-size: 20px 20px;"></div>
    </div>
    
    <!-- Gradient Overlays -->
    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500 to-transparent"></div>
    <div class="absolute -top-32 left-1/4 w-64 h-64 bg-blue-500/10 rounded-full blur-3xl"></div>
    <div class="absolute -top-32 right-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-12">
        
        <!-- Brand Section -->
        <div class="lg:col-span-2">
          <div class="flex items-center space-x-4 mb-8">
            <div class="relative group">
              <div class="w-14 h-14 bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 rounded-2xl flex items-center justify-center shadow-2xl group-hover:shadow-blue-500/25 transition-all duration-500">
                <LucideIcon name="brain" class="w-8 h-8 text-white" />
              </div>
              <div class="absolute -inset-1 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl opacity-20 group-hover:opacity-40 transition-opacity blur"></div>
            </div>
            <div>
              <span class="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                Test Pro
              </span>
              <p class="text-sm text-gray-400 font-medium">Intelligence Testing Platform</p>
            </div>
          </div>
          
          <p class="text-gray-300 mb-8 max-w-lg leading-relaxed text-lg">
            Nền tảng kiểm tra IQ chuyên nghiệp với các bài test chuẩn quốc tế, 
            giúp bạn <span class="text-blue-400 font-semibold">khám phá tiềm năng trí tuệ</span> 
            và phát triển bản thân một cách toàn diện.
          </p>
          
          <!-- Stats -->
          <div class="grid grid-cols-3 gap-6 mb-8">
            <div class="text-center">
              <div class="text-2xl font-bold text-blue-400 mb-1">50K+</div>
              <div class="text-sm text-gray-400">Người dùng</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-green-400 mb-1">1M+</div>
              <div class="text-sm text-gray-400">Bài test</div>
            </div>
            <div class="text-center">
              <div class="text-2xl font-bold text-purple-400 mb-1">99.9%</div>
              <div class="text-sm text-gray-400">Độ chính xác</div>
            </div>
          </div>
          
          <!-- Social Links -->
          <div class="flex space-x-4">
            {[
              { name: 'facebook', label: 'Facebook', color: 'hover:text-blue-400' },
              { name: 'twitter', label: 'Twitter', color: 'hover:text-sky-400' },
              { name: 'instagram', label: 'Instagram', color: 'hover:text-pink-400' },
              { name: 'linkedin', label: 'LinkedIn', color: 'hover:text-blue-500' },
              { name: 'youtube', label: 'YouTube', color: 'hover:text-red-400' }
            ].map(social => (
              <a 
                href="#" 
                class={`w-12 h-12 bg-white/5 border border-white/10 rounded-xl flex items-center justify-center text-gray-400 ${social.color} transition-all duration-300 hover:bg-white/10 hover:border-white/20 hover:scale-110 hover:shadow-lg`}
                aria-label={social.label}
              >
                <LucideIcon name={social.name} class="w-5 h-5" />
              </a>
            ))}
          </div>
        </div>

        <!-- Quick Links -->
        <div>
          <h3 class="text-lg font-bold mb-6 text-white">
            <span class="relative">
              Liên kết nhanh
              <div class="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full"></div>
            </span>
          </h3>
          <ul class="space-y-4">
            {[
              { href: '/', label: 'Trang chủ', icon: '🏠' },
              { href: '/test/iq', label: 'IQ Test', icon: '🧠' },
              { href: '/test/eq', label: 'EQ Test', icon: '❤️' },
              { href: '/leaderboard', label: 'Bảng xếp hạng', icon: '🏆' },
              { href: '/blog', label: 'Blog', icon: '📖' },
              { href: '/about', label: 'Về chúng tôi', icon: 'ℹ️' }
            ].map(link => (
              <li>
                <a 
                  href={link.href} 
                  class="group flex items-center space-x-3 text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1"
                >
                  <span class="text-sm">{link.icon}</span>
                  <span class="group-hover:text-blue-400 transition-colors duration-300">{link.label}</span>
                </a>
              </li>
            ))}
          </ul>
        </div>

        <!-- Support & Help -->
        <div>
          <h3 class="text-lg font-bold mb-6 text-white">
            <span class="relative">
              Hỗ trợ & Giúp đỡ
              <div class="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full"></div>
            </span>
          </h3>
          <ul class="space-y-4 mb-8">
            {[
              { href: '/help', label: 'Trung tâm trợ giúp', icon: '🆘' },
              { href: '/contact', label: 'Liên hệ', icon: '📞' },
              { href: '/faq', label: 'Câu hỏi thường gặp', icon: '❓' },
              { href: '/support', label: 'Hỗ trợ kỹ thuật', icon: '🔧' },
              { href: '/feedback', label: 'Góp ý', icon: '💬' }
            ].map(link => (
              <li>
                <a 
                  href={link.href} 
                  class="group flex items-center space-x-3 text-gray-300 hover:text-white transition-all duration-300 hover:translate-x-1"
                >
                  <span class="text-sm">{link.icon}</span>
                  <span class="group-hover:text-green-400 transition-colors duration-300">{link.label}</span>
                </a>
              </li>
            ))}
          </ul>
          
          <!-- Contact Info -->
          <div class="space-y-3">
            <div class="flex items-center space-x-3 text-gray-300">
              <LucideIcon name="mail" class="w-4 h-4 text-blue-400" />
              <span class="text-sm"><EMAIL></span>
            </div>
            <div class="flex items-center space-x-3 text-gray-300">
              <LucideIcon name="phone" class="w-4 h-4 text-green-400" />
              <span class="text-sm">+84 (0) 123 456 789</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="mt-16 pt-8 border-t border-white/10">
        <div class="flex flex-col lg:flex-row justify-between items-center space-y-6 lg:space-y-0">
          
          <!-- Copyright -->
          <div class="text-center lg:text-left">
            <p class="text-gray-400 text-sm">
              © 2024 <span class="text-white font-semibold">Test Pro</span>. 
              Tất cả quyền được bảo lưu.
            </p>
            <p class="text-gray-500 text-xs mt-1">
              Được phát triển với ❤️ tại Việt Nam
            </p>
          </div>

          <!-- Legal Links -->
          <div class="flex items-center space-x-6 text-sm">
            {[
              { href: '/privacy', label: 'Chính sách bảo mật' },
              { href: '/terms', label: 'Điều khoản sử dụng' },
              { href: '/cookies', label: 'Cookie Policy' }
            ].map((link, index) => (
              <>
                <a 
                  href={link.href} 
                  class="text-gray-400 hover:text-white transition-colors duration-300"
                >
                  {link.label}
                </a>
                {index < 2 && <span class="text-gray-600">•</span>}
              </>
            ))}
          </div>

          <!-- Trust Badges -->
          <div class="flex items-center space-x-6">
            <div class="flex items-center space-x-2 text-gray-400 text-sm bg-white/5 px-3 py-2 rounded-lg border border-white/10">
              <LucideIcon name="shield-check" class="w-4 h-4 text-green-400" />
              <span>SSL Secured</span>
            </div>
            <div class="flex items-center space-x-2 text-gray-400 text-sm bg-white/5 px-3 py-2 rounded-lg border border-white/10">
              <LucideIcon name="zap" class="w-4 h-4 text-yellow-400" />
              <span>Fast & Reliable</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>

  <!-- Performance Scripts -->
  <script>
    window.addEventListener('load', () => {
      if ('performance' in window) {
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        console.log(`Page loaded in ${loadTime}ms`);
      }
    });

    if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
          .then(registration => console.log('SW registered'))
          .catch(error => console.log('SW registration failed'));
      });
    }
  </script>
</body>
</html>