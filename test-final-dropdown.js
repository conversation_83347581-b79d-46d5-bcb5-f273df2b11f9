// Final test script for language dropdown - paste into browser console
console.log('🔍 FINAL DROPDOWN TEST');
console.log('====================');

// Wait for page to load
setTimeout(() => {
  console.log('\n1. 🔍 SEARCHING FOR LANGUAGE BUTTON...');
  
  // Find button by data-testid
  const languageButton = document.querySelector('[data-testid="language-selector-button"]');
  console.log('Language button by testid:', languageButton);
  
  if (languageButton) {
    console.log('✅ FOUND LANGUAGE BUTTON');
    console.log('Button text:', languageButton.textContent);
    console.log('Button classes:', languageButton.className);
    
    // Check if dropdown exists (should always exist now)
    const dropdown = document.querySelector('[data-testid="language-dropdown"]');
    console.log('Dropdown element:', dropdown);
    
    if (dropdown) {
      console.log('✅ DROPDOWN ELEMENT EXISTS');
      console.log('Initial dropdown classes:', dropdown.className);
      console.log('Initial dropdown styles:', {
        opacity: getComputedStyle(dropdown).opacity,
        visibility: getComputedStyle(dropdown).visibility,
        transform: getComputedStyle(dropdown).transform,
        display: getComputedStyle(dropdown).display
      });
      
      // Test click
      console.log('\n2. 🖱️ TESTING BUTTON CLICK...');
      languageButton.click();
      
      // Check dropdown after click
      setTimeout(() => {
        console.log('\n3. 📊 DROPDOWN STATE AFTER CLICK:');
        console.log('Dropdown classes after click:', dropdown.className);
        console.log('Dropdown styles after click:', {
          opacity: getComputedStyle(dropdown).opacity,
          visibility: getComputedStyle(dropdown).visibility,
          transform: getComputedStyle(dropdown).transform,
          display: getComputedStyle(dropdown).display,
          pointerEvents: getComputedStyle(dropdown).pointerEvents
        });
        
        // Check if dropdown is visible
        const isVisible = getComputedStyle(dropdown).opacity === '1' && 
                         getComputedStyle(dropdown).visibility === 'visible';
        
        if (isVisible) {
          console.log('✅ SUCCESS: Dropdown is visible!');
          
          // Test language selection
          const languageOptions = dropdown.querySelectorAll('button');
          console.log(`Found ${languageOptions.length} language options`);
          
          if (languageOptions.length > 1) {
            console.log('\n4. 🖱️ TESTING LANGUAGE SELECTION...');
            const secondOption = languageOptions[1]; // Select second language
            console.log('Clicking second language option:', secondOption.textContent);
            secondOption.click();
            
            setTimeout(() => {
              console.log('\n5. 📊 STATE AFTER LANGUAGE SELECTION:');
              console.log('Button text after selection:', languageButton.textContent);
              console.log('Dropdown visibility after selection:', {
                opacity: getComputedStyle(dropdown).opacity,
                visibility: getComputedStyle(dropdown).visibility
              });
              
              const isHidden = getComputedStyle(dropdown).opacity === '0' || 
                              getComputedStyle(dropdown).visibility === 'hidden';
              
              if (isHidden) {
                console.log('✅ SUCCESS: Dropdown closed after selection!');
              } else {
                console.log('❌ ISSUE: Dropdown still visible after selection');
              }
            }, 200);
          }
          
        } else {
          console.log('❌ ISSUE: Dropdown is not visible after click');
          console.log('Checking for CSS conflicts...');
          
          // Check for CSS that might be hiding it
          const allStyles = getComputedStyle(dropdown);
          console.log('All computed styles:', {
            position: allStyles.position,
            top: allStyles.top,
            right: allStyles.right,
            zIndex: allStyles.zIndex,
            backgroundColor: allStyles.backgroundColor,
            border: allStyles.border,
            width: allStyles.width,
            height: allStyles.height
          });
        }
      }, 300);
      
    } else {
      console.log('❌ DROPDOWN ELEMENT NOT FOUND');
    }
    
  } else {
    console.log('❌ LANGUAGE BUTTON NOT FOUND');
    
    // Search for any button with flag or VI text
    const allButtons = document.querySelectorAll('button');
    console.log(`Searching ${allButtons.length} buttons for language button...`);
    
    allButtons.forEach((btn, index) => {
      const text = btn.textContent || '';
      if (text.includes('🇻🇳') || text.includes('VI') || text.includes('EN')) {
        console.log(`Found potential language button at index ${index}:`, btn);
        console.log('Text:', text);
      }
    });
  }
  
}, 2000);

// Also check for React state
setTimeout(() => {
  console.log('\n6. 🔍 CHECKING REACT STATE...');
  
  // Check if React DevTools is available
  if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
    console.log('React DevTools available');
  }
  
  // Check for any React errors in console
  const originalError = console.error;
  console.error = function(...args) {
    if (args[0] && args[0].toString().includes('React')) {
      console.log('🚨 REACT ERROR DETECTED:', args);
    }
    originalError.apply(console, args);
  };
  
}, 3000);
