/** @type {import('tailwindcss').Config} */
export default {
    content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
    darkMode: 'class',
    theme: {
      extend: {
        colors: {
          primary: {
            50: '#eff6ff',
            100: '#dbeafe',
            200: '#bfdbfe',
            300: '#93c5fd',
            400: '#60a5fa',
            500: '#3b82f6',
            600: '#2563eb',
            700: '#1d4ed8',
            800: '#1e40af',
            900: '#1e3a8a',
            950: '#172554'
          },
          accent: {
            50: '#fdf4ff',
            100: '#fae8ff',
            200: '#f5d0fe',
            300: '#f0abfc',
            400: '#e879f9',
            500: '#d946ef',
            600: '#c026d3',
            700: '#a21caf',
            800: '#86198f',
            900: '#701a75',
            950: '#4a044e'
          },
          success: {
            50: '#f0fdf4',
            100: '#dcfce7',
            200: '#bbf7d0',
            300: '#86efac',
            400: '#4ade80',
            500: '#22c55e',
            600: '#16a34a',
            700: '#15803d',
            800: '#166534',
            900: '#14532d',
            950: '#052e16'
          }
        },
        fontFamily: {
          sans: ['Inter', 'system-ui', 'sans-serif'],
          display: ['Space Grotesk', 'Inter', 'system-ui', 'sans-serif']
        },
        animation: {
          // 🚀 Siêu tối ưu animations - Siêu nhanh, siêu mượt
          'popup-in': 'popupIn 0.12s cubic-bezier(0.16, 1, 0.3, 1)',
          'popup-out': 'popupOut 0.08s ease-out',
          'fade-fast': 'fadeIn 0.08s ease-out',
          'scale-fast': 'scaleIn 0.12s cubic-bezier(0.16, 1, 0.3, 1)',
          'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite'
        },
        keyframes: {
          // 🚀 Siêu tối ưu keyframes - Hardware accelerated
          fadeIn: {
            '0%': { opacity: '0' },
            '100%': { opacity: '1' }
          },
          popupIn: {
            '0%': { transform: 'scale(0.92) translateZ(0)', opacity: '0' },
            '100%': { transform: 'scale(1) translateZ(0)', opacity: '1' }
          },
          popupOut: {
            '0%': { transform: 'scale(1) translateZ(0)', opacity: '1' },
            '100%': { transform: 'scale(0.92) translateZ(0)', opacity: '0' }
          },
          scaleIn: {
            '0%': { transform: 'scale(0.92) translateZ(0)', opacity: '0' },
            '100%': { transform: 'scale(1) translateZ(0)', opacity: '1' }
          }
        }
      }
    },
    plugins: []
  };