/**
 * 🚀 <PERSON>êu tối ưu popup animations - <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> mư<PERSON>, <PERSON><PERSON><PERSON> nhẹ
 * <PERSON><PERSON><PERSON> hình animation thống nhất cho toàn bộ hệ thống popup
 */

// ⚡ Animation config siêu tối ưu - 60fps, hardware accelerated
export const POPUP_ANIMATIONS = {
  // 🎯 Backdrop overlay - <PERSON><PERSON><PERSON> nhanh
  backdrop: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
    transition: { duration: 0.08, ease: "easeOut" }
  },

  // 🎯 Modal content - Siêu mượt với scale + opacity
  modal: {
    initial: { scale: 0.92, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 0.92, opacity: 0 },
    transition: { duration: 0.12, ease: [0.16, 1, 0.3, 1] } // Custom cubic-bezier siêu mượt
  },

  // 🎯 Form switching - Siêu nhẹ
  form: {
    initial: { opacity: 0, x: 8 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -8 },
    transition: { duration: 0.1, ease: "easeOut" }
  },

  // 🎯 Notification popup - Slide từ trên
  notification: {
    initial: { opacity: 0, y: -12, scale: 0.96 },
    animate: { opacity: 1, y: 0, scale: 1 },
    exit: { opacity: 0, y: -12, scale: 0.96 },
    transition: { duration: 0.1, ease: "easeOut" }
  }
} as const;

// 🎯 Animation variants cho các loại popup khác nhau
export const getPopupAnimation = (type: 'modal' | 'notification' | 'form' = 'modal') => {
  return POPUP_ANIMATIONS[type];
};

// 🎯 Animation cho backdrop
export const getBackdropAnimation = () => {
  return POPUP_ANIMATIONS.backdrop;
};

// ⚡ Optimized AnimatePresence props
export const ANIMATE_PRESENCE_CONFIG = {
  mode: "wait" as const,
  initial: false // Tắt initial animation để tăng performance
};

// 🎯 Layout animation config - Tắt layout animation để tăng performance
export const LAYOUT_CONFIG = {
  layoutId: undefined, // Tắt layout animation
  layout: false // Tắt layout animation
};
